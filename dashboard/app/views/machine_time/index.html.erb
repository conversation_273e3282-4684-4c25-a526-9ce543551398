<% content_for :content do %>
  <style>
    .content-body {
      height: calc(100vh - 100px);
      background: white;
      border-radius: 16px;
      padding: 22px 30px;
    }
    .submit-button {
      font-size: 14px;
      padding: 12px;
      background: #007Aff;
    }
    .avatar-container {
      text-align: center;
    }
  </style>
  <div class="header" style="display: flex;justify-content: space-between;">
    <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
    <div style="display: flex;align-items:center;">
      <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
      <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
    </div>
  </div>
  <div class="content-body">
    <a class="logout-text" href="/pun/sys/dashboard">
      <%= image_tag "/new/home/<USER>", style: "width: 30px;height: 30px;" %>
    </a>
    <span style="font-size: 18px;font-weight: 500;margin-top: 8px;">机时记录</span>
    <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
      <div style="width: 1280px;display: flex;justify-content:center;" class="row">
        <div class="col-1211" style="width: 640px;">
          <div class="d-flex justify-content-center border-bottom mb-2" id="resourceTabs" role="tablist" style="gap: 2rem;font-size: 18px;">
            <%= link_to machine_time_path(tab: 'cpu', page: params[:page]),
                class: "pb-2 tab-underline-link #{'tab-underline-active' if @current_tab == 'cpu'}",
                id: "cpu-tab",
                role: "tab",
                'aria-controls': "cpu",
                'aria-selected': (@current_tab == 'cpu') do %>
              CPU
            <% end %>
            <%= link_to machine_time_path(tab: 'gpu', page: params[:page]),
                class: "pb-2 tab-underline-link #{'tab-underline-active' if @current_tab == 'gpu'}",
                id: "gpu-tab",
                role: "tab",
                'aria-controls': "gpu",
                'aria-selected': (@current_tab == 'gpu') do %>
              GPU
            <% end %>
            <%= link_to machine_time_path(tab: 'ram', page: params[:page]),
                class: "pb-2 tab-underline-link #{'tab-underline-active' if @current_tab == 'ram'}",
                id: "ram-tab",
                role: "tab",
                'aria-controls': "ram",
                'aria-selected': (@current_tab == 'ram') do %>
              RAM
            <% end %>
          </div>
          <style>
            .tab-underline-link {
              color: #495057;
              text-decoration: none;
              font-weight: 500;
              border: none;
              border-bottom: 2px solid transparent;
              transition: border-color 0.2s, color 0.2s;
              margin-right: 1.5rem;
            }
            .tab-underline-link:hover, .tab-underline-link:focus {
              color: #007bff;
              text-decoration: none;
            }
            .tab-underline-active {
              color: #007bff;
              border-bottom: 2.5px solid #007bff;
            }
          </style>
        </div>
        <!-- 使用记录列表 -->
        <div class="col-1211" style="width: 640px;">
          <div class="card" style="border: none;">
            <div class="card-body p-0">
              <div class="table-responsive" style="height: calc(100vh - 256px);">
                <% if @machine_time_records.any? %>
                  <% @machine_time_records.each_with_index do |record, index| %>
                    <div style="border-bottom: 1px;margin-top: 22px;margin-bottom:33px;">
                      <div style="display: flex;justify-content:space-between;font-size: 14px;font-weight: 600;">
                        <div><%= record[:task_name] %></div>
                        <div style="color:#FF4949;"><%= record[:usage_time] %></div>
                      </div>
                      <div style="font-size: 12px;"><%= record[:start_time] %></div>
                      <div style="font-size: 12px;color:#666666;display: flex;justify-content:space-between;">
                        <div><%= record[:remaining_time] %></div>
                        <div><%= record[:remaining_machine_time] %></div>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <div class="text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p class="mb-0">暂无记录</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<%= render 'layouts/dashboard/base' %>