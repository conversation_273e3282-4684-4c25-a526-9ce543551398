<% content_for :content do %>
  <style>
    .content-body {
      height: calc(100vh - 100px);
      background: white;
      border-radius: 16px;
      padding: 22px 30px;
    }

    .tab-item {
      padding-bottom: 8px;
      cursor: pointer;
      color: #495057;
      text-decoration: none;
      font-weight: 500;
      border: none;
      border-bottom: 2px solid transparent;
      transition: border-color 0.2s, color 0.2s;
      margin-right: 1.5rem;
    }

    .tab-item.active {
      color: #007bff;
      border-bottom: 2.5px solid #007bff;
    }

    .tab-item:hover {
      color: #007bff;
      text-decoration: none;
    }

    .job-table-container {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .job-table {
      width: 100%;
      border-collapse: collapse;
    }

    .job-table th {
      background: #f8f9fa;
      padding: 12px 16px;
      text-align: left;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #e9ecef;
    }

    .job-table td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      color: #666;
    }

    .job-table tr:hover {
      background: #f8f9fa;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-completed {
      background: #d4edda;
      color: #155724;
    }

    .status-running {
      background: #cce5ff;
      color: #004085;
    }

    .status-queued {
      background: #fff3cd;
      color: #856404;
    }

    .status-failed {
      background: #f8d7da;
      color: #721c24;
    }

    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #5B93FF;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;
    }

    .empty-state img {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .action-btn {
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      margin-right: 4px;
    }

    .btn-delete {
      background: #dc3545;
      color: white;
    }

    .btn-delete:hover {
      background: #c82333;
    }

    .btn-view {
      background: #007bff;
      color: white;
    }

    .btn-view:hover {
      background: #0056b3;
    }
  </style>

  <div class="header" style="display: flex;justify-content: space-between;">
    <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
    <div style="display: flex;align-items:center;">
      <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
      <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
    </div>
  </div>

  <div class="content-body">
    <a class="logout-text" href="/pun/sys/dashboard">
      <%= image_tag "/new/home/<USER>", style: "width: 30px;height: 30px;" %>
    </a>
    <span style="font-size: 18px;font-weight: 500;margin-top: 8px;">我的任务</span>
    <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
      <div style="width: 1280px;display: flex;justify-content:center;" class="row">
        <div class="col-1211" style="width: 640px;">
          <div class="d-flex justify-content-center border-bottom mb-2" id="statusTabs" role="tablist" style="gap: 2rem;font-size: 18px;">
            <div class="tab-item" data-status="all" role="tab" aria-controls="all" aria-selected="false">全部任务</div>
            <div class="tab-item" data-status="running" role="tab" aria-controls="running" aria-selected="false">运行中</div>
            <div class="tab-item" data-status="queued" role="tab" aria-controls="queued" aria-selected="false">排队中</div>
            <div class="tab-item" data-status="completed" role="tab" aria-controls="completed" aria-selected="false">已完成</div>
            <div class="tab-item" data-status="failed" role="tab" aria-controls="failed" aria-selected="false">失败</div>
          </div>
        </div>
        <!-- 任务管理内容 -->
        <div class="col-1211" style="width: 1280px;">
          <div class="card" style="border: none;">

            <!-- 搜索栏 -->
            <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
              <div style="display: flex; gap: 10px; align-items: center;">
                <input type="text" id="search-input" placeholder="搜索任务名称或ID..."
                       style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">
                <button onclick="refreshData()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                  刷新
                </button>
              </div>
              <div id="job-count" style="color: #666; font-size: 14px;">
                <!-- 任务数量统计 -->
              </div>
            </div>

            <!-- 加载状态 -->
            <div id="loading-state" class="loading-spinner">
              <div class="spinner"></div>
            </div>

            <!-- 任务表格 -->
            <div id="job-table-wrapper" class="job-table-container" style="display: none;">
              <table class="job-table">
                <thead>
                  <tr>
                    <th>任务ID</th>
                    <th>任务名称</th>
                    <th>用户</th>
                    <th>账户</th>
                    <th>队列</th>
                    <th>状态</th>
                    <th>集群</th>
                    <th>运行时间</th>
                    <th>节点</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="job-table-body">
                  <!-- 动态加载的任务数据 -->
                </tbody>
              </table>
            </div>

            <!-- 空状态 -->
            <div id="empty-state" class="empty-state" style="display: none;">
              <div>📋</div>
              <div>暂无任务数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let currentStatus = 'all';
    let allJobs = [];
    let searchTerm = '';

    // 从URL获取状态参数
    function getStatusFromUrl() {
      // 首先检查查询参数
      const urlParams = new URLSearchParams(window.location.search);
      const queryStatus = urlParams.get('status');
      if (queryStatus) {
        return queryStatus;
      }

      // 然后检查路径参数
      const pathParts = window.location.pathname.split('/');
      const statusIndex = pathParts.indexOf('status');
      if (statusIndex !== -1 && statusIndex + 1 < pathParts.length) {
        const pathStatus = pathParts[statusIndex + 1];
        if (['running', 'queued', 'completed', 'failed'].includes(pathStatus)) {
          return pathStatus;
        }
      }

      return 'all';
    }

    // 更新URL参数
    function updateUrl(status) {
      const url = new URL(window.location);
      if (status === 'all') {
        url.searchParams.delete('status');
      } else {
        url.searchParams.set('status', status);
      }
      window.history.pushState({}, '', url);
    }

    // 设置活动tab
    function setActiveTab(status) {
      document.querySelectorAll('.tab-item').forEach(t => {
        t.classList.remove('active');
        t.setAttribute('aria-selected', 'false');
      });
      const activeTab = document.querySelector(`[data-status="${status}"]`);
      if (activeTab) {
        activeTab.classList.add('active');
        activeTab.setAttribute('aria-selected', 'true');
      }
    }

    // Tab切换功能
    document.querySelectorAll('.tab-item').forEach(tab => {
      tab.addEventListener('click', function() {
        // 更新当前状态
        currentStatus = this.dataset.status;

        // 设置活动tab
        setActiveTab(currentStatus);

        // 更新URL
        updateUrl(currentStatus);

        // 过滤并显示数据
        filterAndDisplayJobs();
      });
    });

    // 搜索功能
    document.addEventListener('DOMContentLoaded', function() {
      const searchInput = document.getElementById('search-input');
      searchInput.addEventListener('input', function() {
        searchTerm = this.value.toLowerCase();
        filterAndDisplayJobs();
      });
    });

    // 获取任务数据
    function fetchJobData() {
      showLoading();

      fetch('/pun/sys/dashboard/activejobs.json?jobcluster=all&jobfilter=user')
        .then(response => response.json())
        .then(data => {
          if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
            allJobs = data.data[0] || [];
          } else {
            allJobs = [];
          }
          filterAndDisplayJobs();
        })
        .catch(error => {
          console.error('获取任务数据失败:', error);
          hideLoading();
          showEmptyState();
        });
    }

    // 过滤并显示任务
    function filterAndDisplayJobs() {
      let filteredJobs = allJobs;

      // 按状态过滤
      if (currentStatus !== 'all') {
        filteredJobs = filteredJobs.filter(job => job.status === currentStatus);
      }

      // 按搜索词过滤
      if (searchTerm) {
        filteredJobs = filteredJobs.filter(job =>
          job.jobname.toLowerCase().includes(searchTerm) ||
          job.pbsid.toLowerCase().includes(searchTerm) ||
          job.username.toLowerCase().includes(searchTerm)
        );
      }

      displayJobs(filteredJobs);
      updateJobCount(filteredJobs.length, allJobs.length);
    }

    // 更新任务数量统计
    function updateJobCount(filtered, total) {
      const countElement = document.getElementById('job-count');
      if (filtered === total) {
        countElement.textContent = `共 ${total} 个任务`;
      } else {
        countElement.textContent = `显示 ${filtered} / ${total} 个任务`;
      }
    }

    // 显示任务数据
    function displayJobs(jobs) {
      hideLoading();

      if (jobs.length === 0) {
        showEmptyState();
        return;
      }

      hideEmptyState();
      showTable();

      const tbody = document.getElementById('job-table-body');
      tbody.innerHTML = '';

      jobs.forEach(job => {
        const row = createJobRow(job);
        tbody.appendChild(row);
      });
    }

    // 创建任务行
    function createJobRow(job) {
      const row = document.createElement('tr');

      const statusClass = getStatusClass(job.status);
      const statusText = getStatusText(job.status);
      const nodes = Array.isArray(job.nodes) ? job.nodes.join(', ') : (job.nodes || '-');
      const walltime = job.walltime_used || '-';

      row.innerHTML = `
        <td>${escapeHtml(job.pbsid)}</td>
        <td>${escapeHtml(job.jobname)}</td>
        <td>${escapeHtml(job.username)}</td>
        <td>${escapeHtml(job.account)}</td>
        <td>${escapeHtml(job.queue)}</td>
        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
        <td>${escapeHtml(job.cluster_title)}</td>
        <td>${walltime}</td>
        <td>${nodes}</td>
        <td>
          ${job.extended_available ? `<button class="action-btn btn-view" onclick="viewJobDetails('${job.pbsid}', '${job.cluster}')">详情</button>` : ''}
          ${job.delete_path && job.status !== 'completed' ? `<button class="action-btn btn-delete" onclick="deleteJob('${job.pbsid}', '${job.cluster}', '${escapeHtml(job.jobname)}')">结束</button>` : ''}
        </td>
      `;

      return row;
    }

    // 获取状态样式类
    function getStatusClass(status) {
      const statusMap = {
        'completed': 'status-completed',
        'running': 'status-running',
        'queued': 'status-queued',
        'queued_held': 'status-queued',
        'suspended': 'status-queued',
        'failed': 'status-failed'
      };
      return statusMap[status] || 'status-queued';
    }

    // 获取状态文本
    function getStatusText(status) {
      const statusMap = {
        'completed': '完成',
        'running': '运行',
        'queued': '排队',
        'queued_held': '回填',
        'suspended': '挂起',
        'failed': '失败'
      };
      return statusMap[status] || status;
    }

    // HTML转义
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // 显示/隐藏状态函数
    function showLoading() {
      document.getElementById('loading-state').style.display = 'flex';
      document.getElementById('job-table-wrapper').style.display = 'none';
      document.getElementById('empty-state').style.display = 'none';
    }

    function hideLoading() {
      document.getElementById('loading-state').style.display = 'none';
    }

    function showTable() {
      document.getElementById('job-table-wrapper').style.display = 'block';
      document.getElementById('empty-state').style.display = 'none';
    }

    function showEmptyState() {
      document.getElementById('job-table-wrapper').style.display = 'none';
      document.getElementById('empty-state').style.display = 'block';
    }

    function hideEmptyState() {
      document.getElementById('empty-state').style.display = 'none';
    }

    // 查看任务详情
    function viewJobDetails(pbsid, cluster) {
      // 创建详情模态框
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
      `;

      const modalContent = document.createElement('div');
      modalContent.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 20px;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
      `;

      modalContent.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h3>任务详情 - ${pbsid}</h3>
          <button onclick="this.closest('.modal-overlay').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
        </div>
        <div id="job-details-content">
          <div class="loading-spinner">
            <div class="spinner"></div>
          </div>
        </div>
      `;

      modal.className = 'modal-overlay';
      modal.appendChild(modalContent);
      document.body.appendChild(modal);

      // 获取详细信息
      fetch(`/pun/sys/dashboard/activejobs/json?pbsid=${pbsid}&cluster=${cluster}`)
        .then(response => response.json())
        .then(data => {
          const detailsContent = document.getElementById('job-details-content');
          if (data.html_extended_panel) {
            detailsContent.innerHTML = data.html_extended_panel;
          } else {
            detailsContent.innerHTML = '<p>无法获取任务详情</p>';
          }
        })
        .catch(error => {
          console.error('获取任务详情失败:', error);
          document.getElementById('job-details-content').innerHTML = '<p>获取任务详情失败</p>';
        });

      // 点击背景关闭模态框
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          modal.remove();
        }
      });
    }

    // 结束任务
    function deleteJob(pbsid, cluster, jobname) {
      if (confirm(`确定要结束任务 "${jobname}" (ID: ${pbsid}) 吗？`)) {
        fetch(`/pun/sys/dashboard/activejobs?cluster=${cluster}&pbsid=${pbsid}`, {
          method: 'DELETE',
          headers: {
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => {
          if (response.ok) {
            alert('任务结束成功');
            fetchJobData(); // 重新加载数据
          } else {
            alert('任务结束失败');
          }
        })
        .catch(error => {
          console.error('结束任务失败:', error);
          alert('任务结束失败');
        });
      }
    }

    // 刷新数据
    function refreshData() {
      fetchJobData();
    }

    // 页面加载完成后获取数据
    document.addEventListener('DOMContentLoaded', function() {
      // 从URL获取初始状态
      currentStatus = getStatusFromUrl();

      // 如果服务器传递了初始状态且URL中没有状态参数，使用服务器状态
      const serverInitialStatus = '<%= @initial_status %>';
      if (serverInitialStatus && serverInitialStatus !== 'all' &&
          currentStatus === 'all' &&
          !window.location.search.includes('status') &&
          !window.location.pathname.includes('/status/')) {
        currentStatus = serverInitialStatus;
        updateUrl(currentStatus);
      }

      setActiveTab(currentStatus);

      fetchJobData();

      // 每30秒自动刷新数据
      setInterval(fetchJobData, 30000);
    });

    // 处理浏览器前进后退
    window.addEventListener('popstate', function() {
      currentStatus = getStatusFromUrl();
      setActiveTab(currentStatus);
      filterAndDisplayJobs();
    });
  </script>
<% end %>
<%= render 'layouts/dashboard/base' %>
