# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_09_15_162038) do

  create_table "account_logs", force: :cascade do |t|
    t.string "username"
    t.decimal "after_balance", precision: 14, scale: 4
    t.decimal "changed_balance", precision: 14, scale: 4
    t.string "title"
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "basic_infos", force: :cascade do |t|
    t.string "mac_address"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "lastest_tasks", force: :cascade do |t|
    t.string "dtype"
    t.string "title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "machine_time_changes", force: :cascade do |t|
    t.decimal "cpu", precision: 14, scale: 4
    t.decimal "gpu", precision: 14, scale: 4
    t.decimal "ram", precision: 14, scale: 4
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "system_data", force: :cascade do |t|
    t.text "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "user_infos", force: :cascade do |t|
    t.string "username"
    t.text "remark"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "avatar"
    t.decimal "balance", precision: 14, scale: 4
  end

end
