# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the rake db:seed (or created alongside the db with db:setup).
#
# Examples:
#
#   cities = City.create([{ name: 'Chicago' }, { name: 'Copenhagen' }])
#   Mayor.create(name: 'Emanuel', city: cities.first)

# 创建账户记录测试数据
if AccountLog.count == 0
  puts "创建账户记录测试数据..."

  # 假设当前用户名为 admin（可以根据实际情况修改）
  username = 'admin'
  balance = 1000.0

  25.times do |i|
    change_amount = rand(-100..100)
    balance += change_amount

    titles = ['充值', '消费', '退款', '奖励', '扣费', '转账']
    descriptions = [
      '系统充值',
      '计算资源消费',
      '任务退款',
      '活动奖励',
      '超时扣费',
      '账户转账'
    ]

    AccountLog.create!(
      username: username,
      changed_balance: change_amount,
      after_balance: balance,
      title: titles.sample,
      description: descriptions.sample,
      created_at: Time.now - (25 - i).days
    )
  end

  puts "已创建 25 条账户记录测试数据"
end
